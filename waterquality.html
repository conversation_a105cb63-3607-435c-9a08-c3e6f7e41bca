<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实科技AI加药系统 - 水质情况</title>
    <script src="js/chart.js"></script>
    <link rel="stylesheet" href="css/font/bootstrap-icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
        }
        .header {
            background-color: #fff;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e1e4e8;
        }
        .header-left {
            display: flex;
            align-items: center;
        }
        .header-left img {
            height: 40px;
            margin-right: 15px;
        }
        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 8px;
        }
        .user-dropdown {
            display: none;
            position: absolute;
            top: 45px;
            right: 0;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 10;
            border-radius: 4px;
        }
        .user-dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: black;
            cursor: pointer;
            font-size: 14px;
        }
        .user-dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .user-info:hover .user-dropdown {
            display: block;
        }
        .fullscreen-btn {
            margin-right: 20px;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        .fullscreen-btn:hover {
            color: #333;
        }
        .sidebar {
            width: 210px;
            background-color: white;
            height: calc(100vh - 60px);
            float: left;
            border-right: 1px solid #e1e4e8;
            overflow-y: auto;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            color: #333;
            cursor: pointer;
        }
        .menu-item.active {
            background-color: #4ecdc4;
            color: white;
        }
        .menu-item i {
            margin-right: 10px;
        }
        .menu-item.with-submenu {
            position: relative;
        }
        .menu-item.with-submenu::after {
            content: "›";
            position: absolute;
            right: 15px;
            transform: rotate(90deg);
        }
        .main-content {
            margin-left: 210px;
            padding: 20px;
        }
        .tab-header {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
        }
        .tab-item.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .tab-item i {
            margin-right: 5px;
            font-size: 16px;
        }
        .tab-item .close-btn {
            margin-left: 8px;
            font-size: 14px;
            opacity: 0.6;
        }
        .tab-item:hover .close-btn {
            opacity: 1;
        }
        .tab-tools {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
        }
        .tab-tools i {
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
        }
        .content-panel {
            background-color: #fff;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .content-header {
            margin-bottom: 20px;
        }
        .water-quality-cards {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .water-quality-card {
            flex-grow: 1;
            padding: 20px;
            border-radius: 8px;
            color: white;
            min-width: 150px;
            display: flex;
            flex-direction: column;
        }
        .water-quality-card .card-title {
            font-size: 14px;
            margin-bottom: 20px;
        }
        .water-quality-card .card-value {
            font-size: 28px;
            font-weight: bold;
        }
        .water-quality-card .card-unit {
            font-size: 14px;
            margin-top: 5px;
        }
        .card-cod {
            background-color: #4ecdc4;
        }
        .card-ammonia {
            background-color: #5d9cec;
        }
        .card-ph {
            background-color: #ffce54;
            color: #333;
        }
        .card-phosphorus {
            background-color: #ed5565;
        }
        .card-nitrogen {
            background-color: #f5a65b;
        }
        .date-picker {
            display: flex;
            align-items: center;
            position: relative;
        }
        .date-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            width: 120px;
            text-align: center;
        }
        .divider {
            margin: 0 10px;
        }
        .chart-filters {
            display: flex;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        .chart-filter {
            margin-right: 15px;
            font-size: 14px;
            color: #333;
            cursor: pointer;
            padding: 5px 0;
            border-bottom: 2px solid transparent;
        }
        .chart-filter.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            position: relative;
            border-bottom: 1px solid #eee;
        }
        .chart-legend {
            display: flex;
            justify-content: flex-end;
            margin-top: 10px;
            gap: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #666;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .more-options {
            position: absolute;
            top: 20px;
            right: 20px;
            cursor: pointer;
            font-size: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="tab-header">
            <div class="tab-item">
                <i class="bi bi-graph-up"></i>
                <span>运行情况</span>
                <i class="bi bi-x close-btn"></i>
            </div>
            <div class="tab-item">
                <i class="bi bi-calculator"></i>
                <span>效益分析</span>
                <i class="bi bi-x close-btn"></i>
            </div>
            <div class="tab-item active">
                <i class="bi bi-droplet"></i>
                <span>水质情况</span>
                <i class="bi bi-x close-btn"></i>
            </div>
        </div>
        
        <div class="content-panel water-quality-panel">
            <div class="content-header">
                <h3>水质情况:</h3>
                <i class="bi bi-three-dots-vertical more-options"></i>
            </div>
            
            <div class="water-quality-cards">
                <div class="water-quality-card card-cod">
                    <div class="card-title">COD</div>
                    <div class="card-value">53.74</div>
                    <div class="card-unit">mg/L</div>
                </div>
                <div class="water-quality-card card-ammonia">
                    <div class="card-title">氨氮</div>
                    <div class="card-value">0.059</div>
                    <div class="card-unit">mg/L</div>
                </div>
                <div class="water-quality-card card-ph">
                    <div class="card-title">pH</div>
                    <div class="card-value">7</div>
                    <div class="card-unit"></div>
                </div>
                <div class="water-quality-card card-phosphorus">
                    <div class="card-title">总磷</div>
                    <div class="card-value">3</div>
                    <div class="card-unit">m³/h</div>
                </div>
                <div class="water-quality-card card-nitrogen">
                    <div class="card-title">总氮</div>
                    <div class="card-value">NaN</div>
                    <div class="card-unit">m³/h</div>
                </div>
            </div>
            
            <div class="chart-filters">
                <div class="chart-filter active">COD</div>
                <div class="chart-filter">氨氮</div>
                <div class="chart-filter">pH</div>
            </div>
            
            <div class="date-picker">
                <span>选择时间:</span>
                <input type="text" class="date-input" id="startDate" value="2025-05-08" readonly>
                <span class="divider">—</span>
                <input type="text" class="date-input" id="endDate" value="2025-05-08" readonly>
                <i class="bi bi-calendar3" id="calendarToggle" style="margin-left: 10px; cursor: pointer;"></i>
            </div>
            
            <div class="chart-container">
                <canvas id="waterQualityChart"></canvas>
            </div>
            
            <div class="chart-legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #673AB7;"></div>
                    <span>排放口COD</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #8BC34A;"></div>
                    <span>终沉池出水COD</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4CAF50;"></div>
                    <span>入水COD</span>
                </div>
            </div>
        </div>
    </div>
    
    <script src="components.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 渲染水质数据图表
            const ctx = document.getElementById('waterQualityChart').getContext('2d');
            
            // 模拟数据
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['5-1', '5-2', '5-3', '5-4', '5-5', '5-6', '5-7', '5-8'],
                    datasets: [
                        {
                            label: '排放口COD',
                            data: [30, 35, 25, 45, 35, 40, 30, 35],
                            borderColor: '#673AB7',
                            backgroundColor: 'transparent',
                            tension: 0.4,
                            pointRadius: 4
                        },
                        {
                            label: '终沉池出水COD',
                            data: [50, 55, 45, 65, 60, 70, 55, 60],
                            borderColor: '#8BC34A',
                            backgroundColor: 'transparent',
                            tension: 0.4,
                            pointRadius: 4
                        },
                        {
                            label: '入水COD',
                            data: [80, 85, 75, 95, 85, 90, 80, 85],
                            borderColor: '#4CAF50',
                            backgroundColor: 'transparent',
                            tension: 0.4,
                            pointRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#333',
                            bodyColor: '#666',
                            borderColor: '#ddd',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });

            // 添加标签切换功能
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabText = this.querySelector('span').textContent;
                    if (tabText === '运行情况') {
                        window.location.href = 'index.html';
                    } else if (tabText === '效益分析') {
                        window.location.href = 'analyze.html';
                    }
                });
            });
            
            // 图表筛选器切换
            const chartFilters = document.querySelectorAll('.chart-filter');
            chartFilters.forEach(filter => {
                filter.addEventListener('click', function() {
                    chartFilters.forEach(f => f.classList.remove('active'));
                    this.classList.add('active');
                    // 这里可以添加切换图表数据的逻辑
                });
            });
        });
    </script>
</body>
</html> 