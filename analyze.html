<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实科技AI加药系统 - 效益分析</title>
    <script src="js/chart.js"></script>
    <link rel="stylesheet" href="css/font/bootstrap-icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
        }
        .header {
            background-color: #fff;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e1e4e8;
        }
        .header-left {
            display: flex;
            align-items: center;
        }
        .header-left img {
            height: 40px;
            margin-right: 15px;
        }
        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 8px;
        }
        .user-dropdown {
            display: none;
            position: absolute;
            top: 45px;
            right: 0;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 10;
            border-radius: 4px;
        }
        .user-dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: black;
            cursor: pointer;
            font-size: 14px;
        }
        .user-dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .user-info:hover .user-dropdown {
            display: block;
        }
        .fullscreen-btn {
            margin-right: 20px;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        .fullscreen-btn:hover {
            color: #333;
        }
        .sidebar {
            width: 210px;
            background-color: white;
            height: calc(100vh - 60px);
            float: left;
            border-right: 1px solid #e1e4e8;
            overflow-y: auto;
        }
        .logo-container {
            padding: 15px;
            border-bottom: 1px solid #e1e4e8;
        }
        .logo {
            height: 50px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            color: #333;
            cursor: pointer;
        }
        .menu-item.active {
            background-color: #4ecdc4;
            color: white;
        }
        .menu-item i {
            margin-right: 10px;
        }
        .menu-item.with-submenu {
            position: relative;
        }
        .menu-item.with-submenu::after {
            content: "›";
            position: absolute;
            right: 15px;
            transform: rotate(90deg);
        }
        .main-content {
            margin-left: 210px;
            padding: 20px;
        }
        .tab-header {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
        }
        .tab-item.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .tab-item i {
            margin-right: 5px;
            font-size: 16px;
        }
        .tab-item .close-btn {
            margin-left: 8px;
            font-size: 14px;
            opacity: 0.6;
        }
        .tab-item:hover .close-btn {
            opacity: 1;
        }
        .tab-tools {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
        }
        .tab-tools i {
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
        }
        .content-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .date-picker {
            display: flex;
            align-items: center;
            position: relative;
        }
        .date-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .date-calendar {
            cursor: pointer;
            margin-left: 10px;
            font-size: 18px;
        }
        .chart-container {
            height: 320px;
            width: 100%;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .summary-cards {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 20px;
        }
        .summary-card {
            flex: 1;
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .card-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
        }
        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .card-value.success {
            color: #4ecdc4;
        }
        .card-value.danger {
            color: #ef5350;
        }
        .card-unit {
            font-size: 14px;
            color: #666;
            margin-left: 2px;
        }
        .two-columns {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .column {
            flex: 1;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th, 
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .data-table th {
            background-color: #f8f9fa;
            font-weight: normal;
            color: #666;
        }
        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .data-table .value-up {
            color: #4caf50;
        }
        .data-table .value-down {
            color: #f44336;
        }
        .expense-category {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        .expense-category:last-child {
            border-bottom: none;
        }
        .expense-name {
            display: flex;
            align-items: center;
        }
        .expense-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 8px;
        }
        .expense-value {
            font-weight: bold;
        }
        .divider {
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <!-- 头部和侧栏将由组件动态插入 -->
    
    <div class="main-content">
        <div class="tab-header">
            <div class="tab-item">
                <i class="bi bi-bar-chart-line"></i>
                <span>运行情况</span>
                <i class="bi bi-x close-btn"></i>
            </div>
            <div class="tab-item active">
                <i class="bi bi-pie-chart"></i>
                <span>效益分析</span>
                <i class="bi bi-x close-btn"></i>
            </div>
            <div class="tab-tools">
                <i class="bi bi-three-dots-vertical"></i>
            </div>
        </div>
        
        <div class="content-panel">
            <div class="content-header">
                <h3>日节约费用: <span style="color: #4ecdc4;">¥1,265</span></h3>
                
                <div class="date-picker">
                    <span>选择时间: </span>
                    <input type="text" class="date-input" id="startDate" readonly value="2025-05-08">
                    <span class="divider">→</span>
                    <input type="text" class="date-input" id="endDate" readonly value="2025-05-08">
                    <span class="date-calendar" id="calendarToggle">
                        <i class="bi bi-calendar3"></i>
                    </span>
                </div>
            </div>
            
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="card-title">今日处理水量</div>
                    <div class="card-value">1,210<span class="card-unit">m³</span></div>
                </div>
                <div class="summary-card">
                    <div class="card-title">月累计处理水量</div>
                    <div class="card-value">36,450<span class="card-unit">m³</span></div>
                </div>
                <div class="summary-card">
                    <div class="card-title">月节约费用</div>
                    <div class="card-value success">¥26,780<span class="card-unit"></span></div>
                </div>
                <div class="summary-card">
                    <div class="card-title">月药剂费用</div>
                    <div class="card-value danger">¥12,450<span class="card-unit"></span></div>
                </div>
            </div>
            
            <div class="two-columns">
                <div class="column">
                    <h3 style="margin-bottom: 15px;">各药剂费用</h3>
                    <div class="chart-container" id="expenseChartContainer">
                        <!-- 药剂费用饼图 -->
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <div class="expense-category">
                            <div class="expense-name">
                                <div class="expense-color" style="background-color: #4ecdc4;"></div>
                                <span>乙酸钠</span>
                            </div>
                            <div class="expense-value">¥3,260</div>
                        </div>
                        <div class="expense-category">
                            <div class="expense-name">
                                <div class="expense-color" style="background-color: #ffc107;"></div>
                                <span>聚合氯化铝（PAC）</span>
                            </div>
                            <div class="expense-value">¥4,680</div>
                        </div>
                        <div class="expense-category">
                            <div class="expense-name">
                                <div class="expense-color" style="background-color: #9c27b0;"></div>
                                <span>硫酸铝（Al2SO4）</span>
                            </div>
                            <div class="expense-value">¥2,150</div>
                        </div>
                        <div class="expense-category">
                            <div class="expense-name">
                                <div class="expense-color" style="background-color: #f44336;"></div>
                                <span>聚丙烯酰胺（PAM）</span>
                            </div>
                            <div class="expense-value">¥1,870</div>
                        </div>
                        <div class="expense-category">
                            <div class="expense-name">
                                <div class="expense-color" style="background-color: #2196f3;"></div>
                                <span>除氟剂</span>
                            </div>
                            <div class="expense-value">¥490</div>
                        </div>
                          <div class="expense-category">
                            <div class="expense-name">
                                <div class="expense-color" style="background-color: #91f321;"></div>
                                <span>氢氧化钠</span>
                            </div>
                            <div class="expense-value">¥4,190</div>
                        </div>
                          <div class="expense-category">
                            <div class="expense-name">
                                <div class="expense-color" style="background-color: #f33a21;"></div>
                                <span>次氯酸钠</span>
                            </div>
                            <div class="expense-value">¥4,890</div>
                        </div>
                    </div>
                </div>
                
                <div class="column">
                    <h3 style="margin-bottom: 15px;">月节约费用趋势</h3>
                    <div class="chart-container" id="savingsChartContainer">
                        <!-- 节约费用柱状图 -->
                    </div>
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <h3 style="margin-bottom: 15px;">各项指标对比</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>指标名称</th>
                            <th>当前数值</th>
                            <th>目标数值</th>
                            <th>AI优化前</th>
                            <th>节约比例</th>
                            <th>趋势</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>吨水药剂费用</td>
                            <td>¥0.34</td>
                            <td>¥0.32</td>
                            <td>¥0.48</td>
                            <td class="value-up">29.2%</td>
                            <td><i class="bi bi-arrow-down-right" style="color: #4caf50;"></i></td>
                        </tr>
                        <tr>
                            <td>污泥处理成本</td>
                            <td>¥0.18</td>
                            <td>¥0.16</td>
                            <td>¥0.22</td>
                            <td class="value-up">18.2%</td>
                            <td><i class="bi bi-arrow-down-right" style="color: #4caf50;"></i></td>
                        </tr>
                        <tr>
                            <td>电耗</td>
                            <td>0.12 kWh/m³</td>
                            <td>0.10 kWh/m³</td>
                            <td>0.15 kWh/m³</td>
                            <td class="value-up">20.0%</td>
                            <td><i class="bi bi-arrow-down" style="color: #4caf50;"></i></td>
                        </tr>
                        <tr>
                            <td>COD去除率</td>
                            <td>92.5%</td>
                            <td>95.0%</td>
                            <td>85.0%</td>
                            <td class="value-up">8.8%</td>
                            <td><i class="bi bi-arrow-up-right" style="color: #4caf50;"></i></td>
                        </tr>
                        <tr>
                            <td>总磷去除率</td>
                            <td>94.2%</td>
                            <td>95.0%</td>
                            <td>86.5%</td>
                            <td class="value-up">8.9%</td>
                            <td><i class="bi bi-arrow-up" style="color: #4caf50;"></i></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 导入组件脚本 -->
    <script src="components.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 页面特定的图表初始化代码
            
            // 饼图 - 药剂费用
            var expenseCtx = document.createElement('canvas');
            expenseCtx.id = 'expenseChart';
            document.getElementById('expenseChartContainer').appendChild(expenseCtx);
            
            var expenseChart = new Chart(expenseCtx, {
                type: 'doughnut',
                data: {
                    labels: ['废酸', '固体硫酸亚铁', '液碱', '双氧水', '盐酸'],
                    datasets: [{
                        data: [3260, 4680, 2150, 1870, 490],
                        backgroundColor: [
                            '#4ecdc4',
                            '#ffc107',
                            '#9c27b0',
                            '#f44336',
                            '#2196f3'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    cutout: '60%'
                }
            });
            
            // 柱状图 - 节约费用趋势
            var savingsCtx = document.createElement('canvas');
            savingsCtx.id = 'savingsChart';
            document.getElementById('savingsChartContainer').appendChild(savingsCtx);
            
            var savingsChart = new Chart(savingsCtx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '节约费用',
                            data: [18650, 21450, 19870, 22680, 24530, 26780],
                            backgroundColor: '#4ecdc4',
                            borderWidth: 0
                        },
                        {
                            label: '药剂费用',
                            data: [15230, 14680, 13950, 13210, 12680, 12450],
                            backgroundColor: '#f44336',
                            borderWidth: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额 (元)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '月份'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html> 