<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实科技AI加药系统 - 运行情况</title>
    <script src="js/chart.js"></script>
    <link rel="stylesheet" href="js/chart.js">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
        }
        .header {
            background-color: #fff;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e1e4e8;
        }
        .header-left {
            display: flex;
            align-items: center;
        }
        .header-left img {
            height: 40px;
            margin-right: 15px;
        }
        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 8px;
        }
        .user-dropdown {
            display: none;
            position: absolute;
            top: 45px;
            right: 0;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 10;
            border-radius: 4px;
        }
        .user-dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: black;
            cursor: pointer;
            font-size: 14px;
        }
        .user-dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .user-info:hover .user-dropdown {
            display: block;
        }
        .fullscreen-btn {
            margin-right: 20px;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        .fullscreen-btn:hover {
            color: #333;
        }
        .sidebar {
            width: 210px;
            background-color: white;
            height: calc(100vh - 60px);
            float: left;
            border-right: 1px solid #e1e4e8;
            overflow-y: auto;
        }
        .logo-container {
            padding: 15px;
            border-bottom: 1px solid #e1e4e8;
        }
        .logo {
            height: 50px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            color: #333;
            cursor: pointer;
        }
        .menu-item.active {
            background-color: #4ecdc4;
            color: white;
        }
        .menu-item i {
            margin-right: 10px;
        }
        .menu-item.with-submenu {
            position: relative;
        }
        .menu-item.with-submenu::after {
            content: "›";
            position: absolute;
            right: 15px;
            transform: rotate(90deg);
        }
        .main-content {
            margin-left: 210px;
            padding: 20px;
        }
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background-color: #fff;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: black;
            cursor: pointer;
        }
        .dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .date-picker {
            display: flex;
            align-items: center;
        }
        .date-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .date-calendar {
            cursor: pointer;
            margin-left: 10px;
            font-size: 18px;
        }
        .divider {
            margin: 0 10px;
        }
        .content-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 15px;
        }
        .chart-container {
            height: 300px;
            width: 100%;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .meter-container {
            padding: 20px;
        }
        .meter-title {
            color: #5b6b7b;
            margin-bottom: 10px;
        }
        .meter-value {
            font-size: 24px;
            font-weight: bold;
        }
        .meter-unit {
            font-size: 18px;
            margin-left: 5px;
        }
        .status-bars {
            margin-top: 20px;
        }
        .status-item {
            margin-bottom: 15px;
        }
        .status-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .progress-bar {
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-value {
            height: 100%;
            background-color: #4ecdc4;
        }
        .legend {
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .normal {
            background-color: #4ecdc4;
        }
        .low {
            background-color: #ffb74d;
        }
        .warning {
            background-color: #ef5350;
        }
        .two-columns {
            display: flex;
            gap: 20px;
        }
        .column {
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- 头部和侧栏将由组件动态插入 -->
    
    <div class="main-content">
        <div class="content-header">
            <div>
                <span>药剂: </span>
                <div class="dropdown">
                    <span>乙酸钠 ▼</span>
                    <div class="dropdown-content">
                        <div class="dropdown-item">聚合氯化铝(PAC)</div>
                        <div class="dropdown-item">硫酸铝(AI2SO4)</div>
                        <div class="dropdown-item">聚丙烯酰胺(PAM)</div>
                        <div class="dropdown-item">除氟剂</div>
                        <div class="dropdown-item">氢氧化钠</div>
                        <div class="dropdown-item">次氯酸钠</div>
                    </div>
                </div>
            </div>
            
            <div class="date-picker">
                <span>选择时间: </span>
                <input type="text" class="date-input" id="startDate" readonly value="2025-05-08">
                <span class="divider">→</span>
                <input type="text" class="date-input" id="endDate" readonly value="2025-05-08">
                <span class="date-calendar" id="calendarToggle">
                    <i class="bi bi-calendar3"></i>
                </span>
            </div>
        </div>
        
        <div class="two-columns">
            <div class="column">
                <div class="content-panel">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <span>流量/</span>
                            <div class="dropdown">
                                <span>固体硫酸亚铁</span>
                            </div>
                        </div>
                        <div>
                            <span>设定量(L/h)</span>
                        </div>
                    </div>
                    
                    <div class="chart-container" id="flowChartContainer">
                        <!-- 这里图表将由JavaScript动态创建 -->
                    </div>
                    
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #4caf50;"></div>
                            <span>铁投加量流量值A</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ffc107;"></div>
                            <span>低聚铁投加量流量值B</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #673ab7;"></div>
                            <span>亚铁投加加流量值A</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #00bcd4;"></div>
                            <span>亚铁投加加流量值B</span>
                        </div>
                    </div>
                </div>
                
                <div class="content-panel">
                    <h3>药剂状态:</h3>
                    
                    <div class="chart-container" id="statusChartContainer">
                        <!-- 这里图表将由JavaScript动态创建 -->
                    </div>
                    
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #4ecdc4;"></div>
                            <span>废酸</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ba68c8;"></div>
                            <span>均值</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #29b6f6;"></div>
                            <span>基准值</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="column">
                <div class="content-panel meter-container">
                    <h3 class="meter-title">日累计处理水量:</h3>
                    <div class="meter-value">
                        <span>m</span>
                        <sup>3</sup>
                    </div>
                </div>
                
                <div class="content-panel meter-container">
                    <h3 class="meter-title">月累计处理水量:</h3>
                    <div class="meter-value">
                        <span>0 m</span>
                        <sup>3</sup>
                    </div>
                </div>
                
                <div class="content-panel">
                    <h3 style="margin-bottom: 20px;">药剂液位:</h3>
                    
                    <div class="status-bars">
                        <div class="status-item">
                            <div class="status-label">
                                <span>乙酸钠:</span>
                                <span>100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: 100%;"></div>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-label">
                                <span>聚合氯化铝（PAC）:</span>
                                <span>100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: 100%;"></div>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-label">
                                <span>硫酸铝（Al2SO4）:</span>
                                <span>100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: 100%;"></div>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-label">
                                <span>聚丙烯酰胺（PAM）:</span>
                                <span>100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: 100%;"></div>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-label">
                                <span>除氟剂:</span>
                                <span>100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: 100%;"></div>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">
                                <span>氢氧化钠:</span>
                                <span>100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: 100%;"></div>
                            </div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">
                                <span>次氯酸钠:</span>
                                <span>100%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-value" style="width: 100%;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color normal"></div>
                            <span>正常</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color low"></div>
                            <span>偏低</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color warning"></div>
                            <span>告警</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 导入组件脚本 -->
    <script src="components.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 流量图表
            var flowCtx = document.createElement('canvas');
            flowCtx.id = 'flowChart';
            document.getElementById('flowChartContainer').appendChild(flowCtx);
            
            var flowChart = new Chart(flowCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],
                    datasets: [
                        {
                            label: '铁投加量流量值A',
                            data: [5, 7, 6, 8, 10, 12, 11, 13, 12, 10, 9, 8],
                            borderColor: '#4caf50',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '低聚铁投加量流量值B',
                            data: [3, 4, 5, 6, 7, 8, 7, 6, 5, 4, 5, 6],
                            borderColor: '#ffc107',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '亚铁投加加流量值A',
                            data: [8, 7, 9, 10, 11, 12, 13, 14, 12, 11, 10, 9],
                            borderColor: '#673ab7',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '亚铁投加加流量值B',
                            data: [10, 11, 12, 13, 14, 15, 14, 13, 12, 11, 12, 13],
                            borderColor: '#00bcd4',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '流量 (L/h)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间'
                            }
                        }
                    }
                }
            });
            
            // 药剂状态图表
            var statusCtx = document.createElement('canvas');
            statusCtx.id = 'statusChart';
            document.getElementById('statusChartContainer').appendChild(statusCtx);
            
            var statusChart = new Chart(statusCtx, {
                type: 'line',
                data: {
                    labels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
                    datasets: [
                        {
                            label: '废酸',
                            data: [80, 85, 83, 87, 90, 88, 86, 89, 91, 92, 90, 88],
                            borderColor: '#4ecdc4',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: '均值',
                            data: [85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85, 85],
                            borderColor: '#ba68c8',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            fill: false,
                            tension: 0
                        },
                        {
                            label: '基准值',
                            data: [90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90, 90],
                            borderColor: '#29b6f6',
                            borderWidth: 2,
                            borderDash: [2, 2],
                            fill: false,
                            tension: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 70,
                            max: 100,
                            title: {
                                display: true,
                                text: '状态 (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '日期'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html> 