// 组件封装文件 - 包含页面通用元素

// 添加全局样式
function addGlobalStyles() {
    // 检查是否已经添加了样式
    if (document.getElementById('components-global-styles')) {
        return;
    }

    const style = document.createElement('style');
    style.id = 'components-global-styles';
    style.textContent = `
        /* 头部样式 */
        .header {
            background-color: #fff;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e1e4e8;
            position: relative;
            z-index: 10;
        }
        .header-left {
            display: flex;
            align-items: center;
        }
        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #4ecdc4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 8px;
        }
        .user-dropdown {
            display: none;
            position: absolute;
            top: 45px;
            right: 0;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 10;
            border-radius: 4px;
        }
        .user-dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: black;
            cursor: pointer;
            font-size: 14px;
        }
        .user-dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .user-info:hover .user-dropdown {
            display: block;
        }
        .fullscreen-btn {
            margin-right: 20px;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        .fullscreen-btn:hover {
            color: #333;
        }

        /* 导航栏样式 */
        .sidebar {
            width: 210px;
            background-color: white;
            height: calc(100vh - 60px);
            float: left;
            border-right: 1px solid #e1e4e8;
            overflow-y: auto;
            transition: width 0.3s;
        }
        .sidebar.collapsed {
            width: 60px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            color: #333;
            cursor: pointer;
            text-decoration: none;
            font-family: "Microsoft YaHei", sans-serif;
        }
        .menu-item.active {
            background-color: #4ecdc4;
            color: white;
        }
        .menu-item i {
            margin-right: 10px;
            font-size: 16px;
        }
        .menu-item.with-submenu {
            position: relative;
        }
        .menu-item.with-submenu::after {
            content: "›";
            position: absolute;
            right: 15px;
            transform: rotate(90deg);
        }
        .sidebar.collapsed .menu-item span,
        .sidebar.collapsed .menu-item.with-submenu::after {
            display: none;
        }
        .sidebar.collapsed .menu-item i {
            margin-right: 0;
        }
        .submenu {
            background-color: #f9f9f9;
            padding-left: 15px;
        }
        .submenu .menu-item {
            padding: 12px 15px;
            font-size: 14px;
        }
        .toggle-sidebar {
            position: fixed;
            left: 210px;
            top: 70px;
            width: 24px;
            height: 24px;
            background-color: #4ecdc4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            z-index: 100;
            transition: left 0.3s;
        }
        .toggle-sidebar.collapsed {
            left: 60px;
        }
        .main-content {
            margin-left: 210px;
            padding: 20px;
            transition: margin-left 0.3s;
        }
        .main-content.expanded {
            margin-left: 60px;
        }
    `;
    document.head.appendChild(style);
}

// 创建头部和侧边栏组件
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已经初始化，避免重复初始化
    if (window.componentsInitialized) return;

    // 如果页面自己会调用初始化函数，则不自动初始化
    const scripts = document.querySelectorAll('script');
    for (let i = 0; i < scripts.length; i++) {
        const scriptContent = scripts[i].textContent;
        if (scriptContent &&
            (scriptContent.includes('addGlobalStyles()') ||
             scriptContent.includes('renderHeader()') ||
             scriptContent.includes('renderSidebar('))) {
            return;
        }
    }

    // 自动初始化
    window.componentsInitialized = true;
    addGlobalStyles();
    renderHeader();
    renderSidebar();
    renderDatePicker();
    renderTabs();
    setupEventListeners();
});
var str = ''
// 渲染头部组件
function renderHeader() {
    const header = document.createElement('div');
    header.className = 'header';
    header.innerHTML = `
        <div class="header-left">
            <img src="logo.png" alt="崇实科技" class="header-logo" style="height: 40px; margin-right: 15px;">
            <div class="header-title">崇实科技AI加药系统</div>
        </div>
        <div style="flex-grow: 1;"></div>
        <div class="fullscreen-btn" id="fullscreenBtn">
            <i class="bi bi-fullscreen"></i>
        </div>
        <div class="user-info">
            <div class="user-avatar">用</div>
            <span>崇实科技</span>
            <i class="bi bi-caret-down-fill" style="margin-left: 5px; font-size: 12px;"></i>
            <div class="user-dropdown">
                <div class="user-dropdown-item">
                    <i class="bi bi-key" style="margin-right: 5px;"></i>修改密码
                </div>
                <div class="user-dropdown-item">
                    <i class="bi bi-box-arrow-right" style="margin-right: 5px;"></i>退出
                </div>
            </div>
        </div>
    `;

    // 将头部添加到body的开始位置，但不影响现有元素
    const firstChild = document.body.firstChild;
    if (firstChild) {
        document.body.insertBefore(header, firstChild);
    } else {
        document.body.appendChild(header);
    }
}

// 渲染侧边栏组件
function renderSidebar(activePage = '') {
    // 获取当前页面的文件名，用于高亮当前页面对应的菜单项
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';

    const sidebar = document.createElement('div');
    sidebar.className = 'sidebar';

    // 根据当前页面或传入的activePage决定哪些菜单项应该高亮显示和展开
    const isRunningPage = (activePage === 'running') || (!activePage && currentPage === 'index.html');
    const isAnalyzePage = (activePage === 'analysis') || (!activePage && currentPage === 'analyze.html');
    const isWaterQualityPage = (activePage === 'waterquality') || (!activePage && currentPage === 'waterquality.html');
    const isDrugParamPage = (activePage === 'drugparam') || (!activePage && currentPage === 'drugparam.html');
    const isDrugInfoPage = (activePage === 'druginfo') || (!activePage && currentPage === 'druginfo.html');
    const isDrugReportPage = (activePage === 'drugreport') || (!activePage && currentPage === 'drugreport.html');
    const isWaterQualityReportPage = (activePage === 'waterqualityreport') || (!activePage && currentPage === 'waterqualityreport.html');
    const isDrugConsumptionReportPage = (activePage === 'drugconsumptionreport') || (!activePage && currentPage === 'drugconsumptionreport.html');
    const isDrugCostPerTonReportPage = (activePage === 'drugcostpertonreport') || (!activePage && currentPage === 'drugcostpertonreport.html');
    const isMonthlySavingReportPage = (activePage === 'monthlysavingreport') || (!activePage && currentPage === 'monthlysavingreport.html');
    const isAbnormalRecordPage = (activePage === 'abnormalrecord') || (!activePage && currentPage === 'abnormalrecord.html');
    const isDepartmentPage = (activePage === 'department') || (!activePage && currentPage === 'department.html');
    const isSystemRolePage = (activePage === 'systemrole') || (!activePage && currentPage === 'systemrole.html');
    const isSystemUserPage = (activePage === 'systemuser') || (!activePage && currentPage === 'systemuser.html');
    const isOperationLogPage = (activePage === 'operationlog') || (!activePage && currentPage === 'operationlog.html');

    sidebar.innerHTML = `
        <a href="index.html" class="menu-item" data-title="概览大屏">
            <i class="bi bi-grid-fill"></i>
            <span>概览大屏</span>
        </a>
        <div class="menu-item with-submenu active" data-title="项目管理">
            <i class="bi bi-clipboard-data"></i>
            <span>项目管理</span>
        </div>
        <div class="submenu" style="display: block; padding-left: 15px; background-color: #f9f9f9;">
            <a href="index.html" class="menu-item ${isRunningPage ? 'active' : ''}" data-title="运行情况">
                <i class="bi bi-activity"></i>
                <span>运行情况</span>
            </a>
            <a href="analyze.html" class="menu-item ${isAnalyzePage ? 'active' : ''}" data-title="效益分析">
                <i class="bi bi-bar-chart-line"></i>
                <span>效益分析</span>
            </a>
            <a href="waterquality.html" class="menu-item ${isWaterQualityPage ? 'active' : ''}" data-title="水质情况">
                <i class="bi bi-droplet"></i>
                <span>水质情况</span>
            </a>
            <a href="drugparam.html" class="menu-item ${isDrugParamPage ? 'active' : ''}" data-title="加药参数">
                <i class="bi bi-sliders"></i>
                <span>加药参数</span>
            </a>
            <a href="druginfo.html" class="menu-item ${isDrugInfoPage ? 'active' : ''}" data-title="药剂信息">
                <i class="bi bi-clipboard-check"></i>
                <span>药剂信息</span>
            </a>
        </div>
        <div class="menu-item with-submenu ${isDrugReportPage || isWaterQualityReportPage || isDrugConsumptionReportPage || isDrugCostPerTonReportPage || isMonthlySavingReportPage || isAbnormalRecordPage ? 'active' : ''}" data-title="报表管理">
            <i class="bi bi-file-earmark-text"></i>
            <span>报表管理</span>
        </div>
        <div class="submenu" style="display: ${isDrugReportPage || isWaterQualityReportPage || isDrugConsumptionReportPage || isDrugCostPerTonReportPage || isMonthlySavingReportPage || isAbnormalRecordPage ? 'block' : 'none'}; padding-left: 15px; background-color: #f9f9f9;">
            <a href="drugreport.html" class="menu-item ${isDrugReportPage ? 'active' : ''}" data-title="系统药剂报表">
                <i class="bi bi-file-text"></i>
                <span>系统药剂报表</span>
            </a>
            <a href="waterqualityreport.html" class="menu-item ${isWaterQualityReportPage ? 'active' : ''}" data-title="水质情况报表">
                <i class="bi bi-droplet-half"></i>
                <span>水质情况报表</span>
            </a>
            <a href="drugconsumptionreport.html" class="menu-item ${isDrugConsumptionReportPage ? 'active' : ''}" data-title="吨水药耗报表">
                <i class="bi bi-calculator"></i>
                <span>吨水药耗报表</span>
            </a>
            <a href="drugcostpertonreport.html" class="menu-item ${isDrugCostPerTonReportPage ? 'active' : ''}" data-title="吨水费用报表">
                <i class="bi bi-currency-yen"></i>
                <span>吨水费用报表</span>
            </a>
            <a href="monthlysavingreport.html" class="menu-item ${isMonthlySavingReportPage ? 'active' : ''}" data-title="月度节约报表">
                <i class="bi bi-piggy-bank"></i>
                <span>月度节约报表</span>
            </a>
            <a href="abnormalrecord.html" class="menu-item ${isAbnormalRecordPage ? 'active' : ''}" data-title="异常情况记录">
                <i class="bi bi-exclamation-triangle"></i>
                <span>异常情况记录</span>
            </a>
        </div>
        <div class="menu-item with-submenu ${isDepartmentPage || isSystemRolePage || isSystemUserPage ? 'active' : ''}" data-title="系统管理">
            <i class="bi bi-gear"></i>
            <span>系统管理</span>
        </div>
        <div class="submenu" style="display: ${isDepartmentPage || isSystemRolePage || isSystemUserPage ? 'block' : 'none'}; padding-left: 15px; background-color: #f9f9f9;">
            <a href="department.html" class="menu-item ${isDepartmentPage ? 'active' : ''}" data-title="部门管理">
                <i class="bi bi-building"></i>
                <span>部门管理</span>
            </a>
            <a href="systemrole.html" class="menu-item ${isSystemRolePage ? 'active' : ''}" data-title="系统角色">
                <i class="bi bi-person-badge"></i>
                <span>系统角色</span>
            </a>
            <a href="systemuser.html" class="menu-item ${isSystemUserPage ? 'active' : ''}" data-title="系统用户">
                <i class="bi bi-person"></i>
                <span>系统用户</span>
            </a>
        </div>
        <div class="menu-item with-submenu ${isOperationLogPage ? 'active' : ''}" data-title="日志管理">
            <i class="bi bi-calendar-date"></i>
            <span>日志管理</span>
        </div>
        <div class="submenu" style="display: ${isOperationLogPage ? 'block' : 'none'}; padding-left: 15px; background-color: #f9f9f9;">
            <a href="operationlog.html" class="menu-item ${isOperationLogPage ? 'active' : ''}" data-title="操作日志记录">
                <i class="bi bi-journal-text"></i>
                <span>操作日志记录</span>
            </a>
        </div>
    `;

    // 将侧边栏添加到body中头部之后，但不影响main-content
    const header = document.querySelector('.header');
    if (header) {
        header.insertAdjacentElement('afterend', sidebar);
    } else {
        document.body.prepend(sidebar);
    }

    // 添加侧边栏切换按钮
    const toggleBtn = document.createElement('div');
    toggleBtn.className = 'toggle-sidebar';
    toggleBtn.innerHTML = '<i class="bi bi-chevron-left"></i>';
    sidebar.after(toggleBtn);

    // 添加侧边栏切换功能
    toggleBtn.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        document.querySelector('.main-content').classList.toggle('expanded');
        toggleBtn.classList.toggle('collapsed');

        // 切换图标
        const icon = toggleBtn.querySelector('i');
        if (sidebar.classList.contains('collapsed')) {
            icon.classList.remove('bi-chevron-left');
            icon.classList.add('bi-chevron-right');
        } else {
            icon.classList.remove('bi-chevron-right');
            icon.classList.add('bi-chevron-left');
        }
    });
}

// 渲染日期选择器组件
function renderDatePicker() {
    // 添加日期选择器样式
    const style = document.createElement('style');
    style.textContent = `
        .date-picker {
            display: flex;
            align-items: center;
            position: relative;
        }
        .date-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .date-calendar {
            cursor: pointer;
            margin-left: 10px;
            font-size: 18px;
        }
        .divider {
            margin: 0 10px;
        }
        .date-dropdown {
            display: none;
            position: absolute;
            top: 40px;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            padding: 15px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 100;
            border-radius: 4px;
            width: 280px;
        }
        .date-dropdown.show {
            display: block;
        }
        .date-preset {
            margin-bottom: 10px;
        }
        .date-preset button {
            margin-right: 5px;
            margin-bottom: 5px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            border-radius: 3px;
            cursor: pointer;
        }
        .date-preset button:hover {
            background-color: #e9ecef;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }
        .calendar-header {
            grid-column: span 7;
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            align-items: center;
        }
        .calendar-day {
            text-align: center;
            padding: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        .calendar-day:hover {
            background-color: #e9ecef;
        }
        .calendar-day.active {
            background-color: #007bff;
            color: white;
        }
        .calendar-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 10px;
        }
        .calendar-actions button {
            padding: 5px 10px;
            margin-left: 10px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            border-radius: 3px;
            cursor: pointer;
        }
        .calendar-actions button.apply {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
    `;
    document.head.appendChild(style);

    // 寻找页面中可能存在的日期选择器容器
    const existingDatePickers = document.querySelectorAll('.date-picker');

    existingDatePickers.forEach(picker => {
        const startDateInput = picker.querySelector('#startDate');
        const endDateInput = picker.querySelector('#endDate');
        const calendarToggle = picker.querySelector('#calendarToggle');

        if (!startDateInput || !endDateInput || !calendarToggle) return;

        // 创建日期选择下拉框
        const dateDropdown = document.createElement('div');
        dateDropdown.className = 'date-dropdown';
        dateDropdown.id = 'dateDropdown';
        dateDropdown.innerHTML = `
            <div class="date-preset">
                <h4 style="margin-bottom: 8px;">快速选择:</h4>
                <button data-days="7">最近7天</button>
                <button data-days="14">最近14天</button>
                <button data-days="30">最近30天</button>
                <button data-type="month">本月</button>
                <button data-type="last-month">上月</button>
            </div>
            <div class="calendar-container">
                <div class="calendar-header">
                    <button id="prevMonth"><i class="bi bi-chevron-left"></i></button>
                    <div id="currentMonth">2025年5月</div>
                    <button id="nextMonth"><i class="bi bi-chevron-right"></i></button>
                </div>
                <div class="calendar-grid" id="calendarDays">
                    <!-- 日历内容将由JS生成 -->
                </div>
            </div>
            <div class="calendar-actions">
                <button id="cancelDate">取消</button>
                <button class="apply" id="applyDate">确定</button>
            </div>
        `;

        picker.appendChild(dateDropdown);
    });
}

// 渲染标签页组件
function renderTabs() {
    // 添加标签页样式
    const style = document.createElement('style');
    style.textContent = `
        .tab-header {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
        }
        .tab-item.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .tab-item i {
            margin-right: 5px;
            font-size: 16px;
        }
        .tab-item .close-btn {
            margin-left: 8px;
            font-size: 14px;
            opacity: 0.6;
        }
        .tab-item:hover .close-btn {
            opacity: 1;
        }
        .tab-tools {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
        }
        .tab-tools i {
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    `;
    document.head.appendChild(style);
}

// 设置通用事件监听器
function setupEventListeners() {
    // 全屏功能
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', function() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log(`错误：${err.message}`);
                });
                fullscreenBtn.innerHTML = '<i class="bi bi-fullscreen-exit"></i>';
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                    fullscreenBtn.innerHTML = '<i class="bi bi-fullscreen"></i>';
                }
            }
        });
    }

    // 子菜单功能
    document.querySelectorAll('div.menu-item.with-submenu').forEach(function(item) {
        item.addEventListener('click', function(e) {
            const submenu = this.nextElementSibling;
            if (submenu && submenu.classList.contains('submenu')) {
                submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
            }
        });
    });

    // 阻止带有子菜单的链接的默认行为
    document.querySelectorAll('a.menu-item.with-submenu').forEach(function(item) {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const submenu = this.nextElementSibling;
            if (submenu && submenu.classList.contains('submenu')) {
                submenu.style.display = submenu.style.display === 'none' ? 'block' : 'none';
            }
        });
    });

    // 不移动main-content，只设置样式
    const mainContent = document.querySelector('.main-content');
    const sidebar = document.querySelector('.sidebar');
    if (mainContent && sidebar) {
        // 确保main-content有正确的margin-left
        mainContent.style.marginLeft = sidebar.classList.contains('collapsed') ? '60px' : '210px';
    }

    // 日期选择器功能
    if (typeof setupDatePicker === 'function') {
        setupDatePicker();
    }

    // 标签页功能
    if (typeof setupTabs === 'function') {
        setupTabs();
    }
}

// 设置日期选择器的事件
function setupDatePicker() {
    // 获取所有日期选择器
    const datePickers = document.querySelectorAll('.date-picker');

    datePickers.forEach(picker => {
        const calendarToggle = picker.querySelector('#calendarToggle');
        const dateDropdown = picker.querySelector('#dateDropdown');
        const startDateInput = picker.querySelector('#startDate');
        const endDateInput = picker.querySelector('#endDate');

        if (!calendarToggle || !dateDropdown || !startDateInput || !endDateInput) return;

        const calendarDays = dateDropdown.querySelector('#calendarDays');
        const currentMonthElem = dateDropdown.querySelector('#currentMonth');
        const prevMonthBtn = dateDropdown.querySelector('#prevMonth');
        const nextMonthBtn = dateDropdown.querySelector('#nextMonth');
        const cancelDateBtn = dateDropdown.querySelector('#cancelDate');
        const applyDateBtn = dateDropdown.querySelector('#applyDate');

        let currentDate = new Date();
        let selectedStartDate = new Date(startDateInput.value);
        let selectedEndDate = new Date(endDateInput.value);

        // 切换日期选择器显示
        calendarToggle.addEventListener('click', function() {
            dateDropdown.classList.toggle('show');
            renderCalendar();
        });

        startDateInput.addEventListener('click', function() {
            dateDropdown.classList.add('show');
            renderCalendar();
        });

        endDateInput.addEventListener('click', function() {
            dateDropdown.classList.add('show');
            renderCalendar();
        });

        // 关闭日期选择器
        document.addEventListener('click', function(e) {
            if (!dateDropdown.contains(e.target) &&
                e.target !== calendarToggle &&
                e.target !== startDateInput &&
                e.target !== endDateInput &&
                !calendarToggle.contains(e.target)) {
                dateDropdown.classList.remove('show');
            }
        });

        // 渲染日历
        function renderCalendar() {
            calendarDays.innerHTML = '';

            const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

            currentMonthElem.textContent = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`;

            // 添加星期标题
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            weekdays.forEach(day => {
                const dayElem = document.createElement('div');
                dayElem.className = 'calendar-day';
                dayElem.style.fontWeight = 'bold';
                dayElem.textContent = day;
                calendarDays.appendChild(dayElem);
            });

            // 添加月初前的空白
            for (let i = 0; i < firstDay.getDay(); i++) {
                const emptyDay = document.createElement('div');
                emptyDay.className = 'calendar-day';
                calendarDays.appendChild(emptyDay);
            }

            // 添加日期
            for (let i = 1; i <= lastDay.getDate(); i++) {
                const dayElem = document.createElement('div');
                dayElem.className = 'calendar-day';
                dayElem.textContent = i;

                const currentDateObj = new Date(currentDate.getFullYear(), currentDate.getMonth(), i);

                // 检查是否为选中日期
                if (isSameDay(currentDateObj, selectedStartDate) || isSameDay(currentDateObj, selectedEndDate)) {
                    dayElem.classList.add('active');
                } else if (currentDateObj > selectedStartDate && currentDateObj < selectedEndDate) {
                    dayElem.style.backgroundColor = '#e6f2ff';
                }

                dayElem.addEventListener('click', function() {
                    if (!selectedStartDate || selectedEndDate) {
                        // 新的选择范围开始
                        selectedStartDate = currentDateObj;
                        selectedEndDate = null;
                        renderCalendar();
                    } else {
                        // 选择范围结束
                        if (currentDateObj < selectedStartDate) {
                            selectedEndDate = selectedStartDate;
                            selectedStartDate = currentDateObj;
                        } else {
                            selectedEndDate = currentDateObj;
                        }
                        renderCalendar();
                    }
                });

                calendarDays.appendChild(dayElem);
            }
        }

        // 检查两个日期是否为同一天
        function isSameDay(date1, date2) {
            return date1.getFullYear() === date2.getFullYear() &&
                   date1.getMonth() === date2.getMonth() &&
                   date1.getDate() === date2.getDate();
        }

        // 格式化日期为yyyy-MM-dd
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 上个月/下个月按钮
        prevMonthBtn.addEventListener('click', function() {
            currentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
            renderCalendar();
        });

        nextMonthBtn.addEventListener('click', function() {
            currentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
            renderCalendar();
        });

        // 快速选择按钮
        dateDropdown.querySelectorAll('.date-preset button').forEach(btn => {
            btn.addEventListener('click', function() {
                const days = this.getAttribute('data-days');
                const type = this.getAttribute('data-type');

                let end = new Date();
                let start = new Date();

                if (days) {
                    start = new Date(end.getTime() - (parseInt(days) * 24 * 60 * 60 * 1000));
                } else if (type === 'month') {
                    start = new Date(end.getFullYear(), end.getMonth(), 1);
                } else if (type === 'last-month') {
                    end = new Date(end.getFullYear(), end.getMonth(), 0);
                    start = new Date(end.getFullYear(), end.getMonth(), 1);
                }

                selectedStartDate = start;
                selectedEndDate = end;
                renderCalendar();
            });
        });

        // 应用选择
        applyDateBtn.addEventListener('click', function() {
            if (selectedStartDate && selectedEndDate) {
                startDateInput.value = formatDate(selectedStartDate);
                endDateInput.value = formatDate(selectedEndDate);
                dateDropdown.classList.remove('show');
            }
        });

        // 取消选择
        cancelDateBtn.addEventListener('click', function() {
            dateDropdown.classList.remove('show');
        });
    });
}

// 设置标签页功能
function setupTabs() {
    const tabHeaders = document.querySelectorAll('.tab-header');

    tabHeaders.forEach(header => {
        const tabItems = header.querySelectorAll('.tab-item');

        // 找到关联的内容面板
        let contentPanels = [];
        const mainContent = header.closest('.main-content') || document.querySelector('.main-content');
        if (mainContent) {
            contentPanels = Array.from(mainContent.querySelectorAll('.content-panel'));
        }

        // 当初始化时确保默认激活第一个标签
        if (tabItems.length > 0 && contentPanels.length > 0) {
            tabItems[0].classList.add('active');

            // 确保所有面板都显示
            for (let i = 0; i < contentPanels.length; i++) {
                contentPanels[i].style.display = 'block';
            }
        }

        tabItems.forEach((item, index) => {
            // 为每个标签添加点击事件
            item.addEventListener('click', function() {
                // 激活点击的标签，取消激活其他标签
                tabItems.forEach(tab => tab.classList.remove('active'));
                item.classList.add('active');

                // 获取当前标签在当前DOM中的实际索引
                const currentIndex = Array.from(header.querySelectorAll('.tab-item')).indexOf(item);

                // 确保所有面板都显示
                contentPanels.forEach(panel => panel.style.display = 'block');
            });

            // 为关闭按钮添加事件
            const closeBtn = item.querySelector('.close-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡

                    // 获取当前所有标签项（实时DOM）
                    const currentTabs = Array.from(header.querySelectorAll('.tab-item'));
                    // 保存被关闭标签的索引
                    const closedIndex = currentTabs.indexOf(item);
                    const isActive = item.classList.contains('active');

                    // 在关闭标签前判断是否需要保留内容面板
                    const shouldPreserveContent = currentTabs.length <= 1 ||
                        (mainContent && mainContent.querySelectorAll('.content-panel').length < contentPanels.length);

                    // 如果关闭的是活动标签，激活相邻标签
                    if (isActive && currentTabs.length > 1) {
                        // 尝试激活下一个标签，如果没有下一个则激活前一个
                        const nextTabIndex = closedIndex < currentTabs.length - 1 ? closedIndex + 1 : closedIndex - 1;
                        const nextTab = currentTabs[nextTabIndex];

                        if (nextTab) {
                            // 如果不是唯一标签，并且有对应的内容面板，才移除内容面板
                            if (!shouldPreserveContent && contentPanels[closedIndex]) {
                                // 从数组中移除内容面板引用，而不是从DOM中移除
                                contentPanels.splice(closedIndex, 1);
                            }

                            // 移除标签
                            item.remove();

                            // 将下一个标签设为活动状态
                            currentTabs.forEach(tab => tab.classList.remove('active'));
                            nextTab.classList.add('active');

                            // 确保所有面板都显示
                            const updatedContentPanels = mainContent.querySelectorAll('.content-panel');
                            Array.from(updatedContentPanels).forEach((panel) => {
                                panel.style.display = 'block';
                            });
                        }
                    } else {
                        // 如果关闭的不是活动标签，不是唯一标签，且有内容面板，则只移除内容面板引用
                        if (!shouldPreserveContent && contentPanels[closedIndex]) {
                            contentPanels.splice(closedIndex, 1);
                        }

                        // 移除标签
                        item.remove();
                    }

                    // 重新对剩余标签进行索引
                    header.querySelectorAll('.tab-item').forEach((tab, idx) => {
                        tab.dataset.index = idx;
                    });
                });
            }
        });
    });
}