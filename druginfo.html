<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实科技AI加药系统 - 药剂信息</title>
    <script src="js/chart.js"></script>
    <link rel="stylesheet" href="css/font/bootstrap-icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
        }
        .header {
            background-color: #fff;
            height: 60px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid #e1e4e8;
        }
        .header-left {
            display: flex;
            align-items: center;
        }
        .header-left img {
            height: 40px;
            margin-right: 15px;
        }
        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 8px;
        }
        .user-dropdown {
            display: none;
            position: absolute;
            top: 45px;
            right: 0;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 10;
            border-radius: 4px;
        }
        .user-dropdown-item {
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            color: black;
            cursor: pointer;
            font-size: 14px;
        }
        .user-dropdown-item:hover {
            background-color: #f1f1f1;
        }
        .user-info:hover .user-dropdown {
            display: block;
        }
        .fullscreen-btn {
            margin-right: 20px;
            cursor: pointer;
            font-size: 24px;
            color: #666;
        }
        .fullscreen-btn:hover {
            color: #333;
        }
        .sidebar {
            width: 210px;
            background-color: white;
            height: calc(100vh - 60px);
            float: left;
            border-right: 1px solid #e1e4e8;
            overflow-y: auto;
            transition: width 0.3s;
        }
        .sidebar.collapsed {
            width: 60px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            color: #333;
            cursor: pointer;
        }
        .menu-item.active {
            background-color: #4ecdc4;
            color: white;
        }
        .menu-item i {
            margin-right: 10px;
        }
        .menu-item.with-submenu {
            position: relative;
        }
        .menu-item.with-submenu::after {
            content: "›";
            position: absolute;
            right: 15px;
            transform: rotate(90deg);
        }
        .sidebar.collapsed .menu-item span,
        .sidebar.collapsed .menu-item.with-submenu::after {
            display: none;
        }
        .sidebar.collapsed .menu-item i {
            margin-right: 0;
        }
        .main-content {
            margin-left: 210px;
            padding: 20px;
            transition: margin-left 0.3s;
        }
        .main-content.expanded {
            margin-left: 60px;
        }
        .tab-header {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
        }
        .tab-item.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .tab-item i {
            margin-right: 5px;
            font-size: 16px;
        }
        .tab-item .close-btn {
            margin-left: 8px;
            font-size: 14px;
            opacity: 0.6;
        }
        .tab-item:hover .close-btn {
            opacity: 1;
        }
        .tab-tools {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
        }
        .tab-tools i {
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
        }
        .content-panel {
            background-color: #fff;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #4ecdc4;
            color: white;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .edit-btn {
            color: #4ecdc4;
            cursor: pointer;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px 10px;
            font-size: 14px;
        }
        .pagination span {
            padding: 5px 10px;
        }
        .toggle-sidebar {
            position: fixed;
            left: 210px;
            top: 70px;
            width: 24px;
            height: 24px;
            background-color: #4ecdc4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            z-index: 100;
            transition: left 0.3s;
        }
        .toggle-sidebar.collapsed {
            left: 60px;
        }
    </style>
</head>
<body>
    <!-- 组件将通过JavaScript注入 -->
    
    <div class="toggle-sidebar">
        <i class="bi bi-chevron-left"></i>
    </div>
    
    <div class="main-content">
        <!-- 标签页头部 -->
        <div class="tab-header">
            <div class="tab-item active">
                <i class="bi bi-flask"></i>
                <span>药剂信息</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-tools">
                <i class="bi bi-three-dots-vertical"></i>
            </div>
        </div>
        
        <!-- 药剂信息内容面板 -->
        <div class="content-panel">
            <h2 style="margin-bottom: 20px;">药剂信息</h2>
            
            <table>
                <thead>
                    <tr>
                        <th style="width: 6%;">序号</th>
                        <th style="width: 10%;">药剂编码</th>
                        <th style="width: 14%;">药剂名称</th>
                        <th style="width: 12%;">单价(元/Kg)</th>
                        <th style="width: 10%;">密度</th>
                        <th style="width: 10%;">比例值</th>
                        <th style="width: 10%;">基准值1</th>
                        <th style="width: 10%;">基准值2</th>
                        <th style="width: 18%;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>YJ001</td>
                        <td>乙酸钠</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>0</td>
                        <td>0</td>
                        <td>
                            <i class="bi bi-pencil-square edit-btn"></i> 编辑
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>YJ002</td>
                        <td>聚合氯化铝（PAC）</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>0.5</td>
                        <td>0.5</td>
                        <td>
                            <i class="bi bi-pencil-square edit-btn"></i> 编辑
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>YJ003</td>
                        <td>硫酸铝（Al2SO4）</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1.8</td>
                        <td>1.8</td>
                        <td>
                            <i class="bi bi-pencil-square edit-btn"></i> 编辑
                        </td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>YJ005</td>
                        <td>聚丙烯酰胺（PAM）</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>0.5</td>
                        <td>0.5</td>
                        <td>
                            <i class="bi bi-pencil-square edit-btn"></i> 编辑
                        </td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>YJ006</td>
                        <td>除氟剂</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>0.4</td>
                        <td>0.6</td>
                        <td>
                            <i class="bi bi-pencil-square edit-btn"></i> 编辑
                        </td>
                    </tr>
                           <td>5</td>
                        <td>YJ006</td>
                        <td>氢氧化钠</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>0.3</td>
                        <td>0.2</td>
                        <td>
                            <i class="bi bi-pencil-square edit-btn"></i> 编辑
                        </td>
                    </tr>
                           <td>5</td>
                        <td>YJ006</td>
                        <td>次氯酸钠</td>
                        <td>1</td>
                        <td>1</td>
                        <td>1</td>
                        <td>0.2</td>
                        <td>0.5</td>
                        <td>
                            <i class="bi bi-pencil-square edit-btn"></i> 编辑
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div class="pagination">
                <button><i class="bi bi-chevron-left"></i></button>
                <span>1</span>
                <button><i class="bi bi-chevron-right"></i></button>
            </div>
        </div>
    </div>
    
    <script src="components.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏收缩功能
            const toggleSidebarBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            
            toggleSidebarBtn.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
                toggleSidebarBtn.classList.toggle('collapsed');
                
                // 切换图标
                const icon = toggleSidebarBtn.querySelector('i');
                if (sidebar.classList.contains('collapsed')) {
                    icon.classList.remove('bi-chevron-left');
                    icon.classList.add('bi-chevron-right');
                } else {
                    icon.classList.remove('bi-chevron-right');
                    icon.classList.add('bi-chevron-left');
                }
            });
            
            // 标签页功能
            const tabItems = document.querySelectorAll('.tab-item');
            const contentPanels = document.querySelectorAll('.content-panel');
            
            tabItems.forEach((tab, index) => {
                tab.addEventListener('click', function() {
                    tabItems.forEach(item => item.classList.remove('active'));
                    tab.classList.add('active');
                    
                    contentPanels.forEach(panel => panel.style.display = 'none');
                    if (contentPanels[index]) {
                        contentPanels[index].style.display = 'block';
                    }
                });
                
                const closeBtn = tab.querySelector('.close-btn');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        
                        const currentTabs = Array.from(document.querySelectorAll('.tab-item'));
                        if (currentTabs.length <= 1) return;
                        
                        const tabIndex = currentTabs.indexOf(tab);
                        const isActive = tab.classList.contains('active');
                        
                        tab.remove();
                        
                        if (isActive) {
                            const nextTabIndex = tabIndex < currentTabs.length - 1 ? tabIndex + 1 : tabIndex - 1;
                            const nextTab = currentTabs[nextTabIndex];
                            
                            if (nextTab) {
                                nextTab.classList.add('active');
                                
                                contentPanels.forEach(panel => panel.style.display = 'none');
                                if (contentPanels[nextTabIndex]) {
                                    contentPanels[nextTabIndex].style.display = 'block';
                                }
                            }
                        }
                    });
                }
            });
        });
    </script>
</body>
</html> 