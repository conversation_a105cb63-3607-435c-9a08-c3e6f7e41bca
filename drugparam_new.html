<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实科技AI加药系统 - 加药参数</title>
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
            padding: 20px;
        }
        .content-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .panel-header {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 16px;
            font-weight: bold;
            background-color: #f5f5f5;
            color: #333;
        }
        .panel-content {
            padding: 15px;
            background-color: #fff;
        }
        .control-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 8px;
        }
        @media (min-width: 1200px) {
            .control-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }
        .control-block {
            background-color: white;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 8px;
        }
        .control-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        .control-label {
            width: 60px;
            color: #666;
            font-size: 13px;
        }
        .control-value {
            flex: 1;
            display: flex;
            align-items: center;
        }
        .control-input {
            width: 50px;
            padding: 4px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            text-align: center;
            font-size: 13px;
        }
        .btn-modify {
            padding: 3px 6px;
            background-color: #4ecdc4;
            color: white;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            margin-left: 8px;
            font-size: 12px;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-on {
            background-color: #52c41a;
        }
        .status-off {
            background-color: #d9d9d9;
        }
        h1 {
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>崇实科技AI加药系统 - 加药参数</h1>

    <!-- 盐酸控制面板 -->
    <div class="content-panel">
        <div class="panel-header">盐酸</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">投加量设定A</div>
                        <div class="control-value">
                            <input type="text" class="control-input" value="1">
                            <button class="btn-modify">修改</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 液碱控制面板 -->
    <div class="content-panel">
        <div class="panel-header">液碱</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">投加量设定A</div>
                        <div class="control-value">
                            <input type="text" class="control-input" value="2">
                            <button class="btn-modify">修改</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 固体硫酸亚铁控制面板 -->
    <div class="content-panel">
        <div class="panel-header">固体硫酸亚铁</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">投加量设定A</div>
                        <div class="control-value">
                            <input type="text" class="control-input" value="1">
                            <button class="btn-modify">修改</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 双氧水控制面板 -->
    <div class="content-panel">
        <div class="panel-header">双氧水</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">投加量设定A</div>
                        <div class="control-value">
                            <input type="text" class="control-input" value="1">
                            <button class="btn-modify">修改</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 废酸-低聚铁面板 -->
    <div class="content-panel">
        <div class="panel-header">废酸-低聚铁</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">投加量设定A</div>
                        <div class="control-value">
                            <input type="text" class="control-input" value="1">
                            <button class="btn-modify">修改</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 废酸-液体硫酸亚铁控制面板 -->
    <div class="content-panel">
        <div class="panel-header">废酸-液体硫酸亚铁</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">投加量设定A</div>
                        <div class="control-value">
                            <input type="text" class="control-input" value="3">
                            <button class="btn-modify">修改</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 氧化池&终沉池 -->
    <div class="content-panel">
        <div class="panel-header">氧化池&终沉池</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">氧化池pH</div>
                        <div class="control-value">
                            <span>4.12</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加药量计算面板 -->
    <div class="content-panel">
        <div class="panel-header">加药量计算</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">H2O2投加比例</div>
                        <div class="control-value">
                            <span>3600 ‰</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 排放口面板 -->
    <div class="content-panel">
        <div class="panel-header">排放口</div>
        <div class="panel-content">
            <div class="control-grid">
                <div class="control-block">
                    <div class="control-row">
                        <div class="control-label">氨氮</div>
                        <div class="control-value">
                            <span>0.06 mg/L</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
