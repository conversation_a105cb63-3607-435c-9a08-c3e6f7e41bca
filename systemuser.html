<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统用户 - 崇实科技AI加药系统</title>
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
        }
        /* 主内容区域样式 */
        .main-content {
            position: relative;
            z-index: 1;
            padding: 20px;
            margin-left: 210px; /* 添加左边距，与侧边栏宽度相同 */
            transition: margin-left 0.3s, width 0.3s;
            width: calc(100% - 210px); /* 设置宽度，避免与侧边栏重叠 */
            float: right; /* 确保在侧边栏右侧 */
            box-sizing: border-box;
        }
        
        /* 当侧边栏折叠时，调整main-content的宽度 */
        .main-content.expanded {
            margin-left: 60px;
            width: calc(100% - 60px);
        }
        
        /* 侧边栏样式优化 */
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 210px;
            height: calc(100vh - 60px);
            background-color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 10;
            transition: width 0.3s;
        }
        
        .sidebar .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: #333;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: background-color 0.2s, border-left-color 0.2s;
        }
        
        .sidebar .menu-item:hover {
            background-color: #f5f5f5;
        }
        
        .sidebar .menu-item.active {
            border-left-color: #4ecdc4;
            background-color: #f9f9f9;
            color: #4ecdc4;
        }
        
        .sidebar .menu-item i {
            margin-right: 10px;
            font-size: 16px;
        }
        
        .sidebar .submenu {
            background-color: #f9f9f9;
            padding-left: 15px;
        }
        
        .sidebar .submenu .menu-item {
            padding: 10px 15px;
        }

        /* 侧边栏切换按钮样式优化 */
        .toggle-sidebar {
            position: fixed;
            left: 210px;
            top: 70px;
            width: 24px;
            height: 24px;
            background-color: #4ecdc4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            z-index: 100;
            transition: left 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .toggle-sidebar:hover {
            background-color: #3dbdb5;
            transform: scale(1.05);
        }
        
        .toggle-sidebar.collapsed {
            left: 60px;
        }
        
        /* 侧边栏折叠状态样式 */
        .sidebar.collapsed {
            width: 60px;
        }
        
        .sidebar.collapsed .menu-item span,
        .sidebar.collapsed .menu-item.with-submenu::after,
        .sidebar.collapsed .submenu {
            display: none !important;
        }
        
        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 15px 0;
        }
        
        .sidebar.collapsed .menu-item i {
            margin-right: 0;
            font-size: 18px;
        }
        
        /* 侧边栏折叠时的悬停提示 */
        .sidebar.collapsed .menu-item {
            position: relative;
        }
        
        .sidebar.collapsed .menu-item:hover::after {
            content: attr(data-title);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            white-space: nowrap;
            z-index: 1000;
            font-size: 12px;
        }

        /* 标签页样式 */
        .tab-header {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 1px;
        }
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
        }
        .tab-item.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .tab-item i {
            margin-right: 5px;
            font-size: 16px;
        }
        .tab-item .close-btn {
            margin-left: 8px;
            font-size: 14px;
            opacity: 0.6;
        }
        .tab-item:hover .close-btn {
            opacity: 1;
        }
        .tab-tools {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
        }
        .tab-tools i {
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
        }

        /* 系统用户内容样式 */
        .user-container {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 20px;
        }
        
        .user-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }
        
        .user-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }
        
        .filter-item {
            display: flex;
            align-items: center;
        }
        
        .filter-label {
            margin-right: 10px;
            color: #666;
        }
        
        .filter-select, .filter-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-width: 200px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        .btn-primary {
            background-color: #4ecdc4;
            color: white;
        }
        
        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
        }
        
        .btn-add {
            background-color: #4ecdc4;
            color: white;
            margin-left: auto;
        }
        
        /* 表格样式 */
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .user-table th, .user-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .user-table th {
            background-color: #f5f5f5;
            font-weight: normal;
            color: #666;
        }
        
        .user-table tr:hover {
            background-color: #f9f9f9;
        }
        
        .action-btn {
            color: #4ecdc4;
            margin: 0 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .action-btn.delete {
            color: #ff6b6b;
        }
        
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 20px;
        }
        
        .page-btn {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ddd;
            margin: 0 5px;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn.active {
            background-color: #4ecdc4;
            color: white;
            border-color: #4ecdc4;
        }
        
        .page-btn:hover:not(.active) {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <!-- 头部和侧栏将由组件动态插入 -->

    <div class="main-content">
        <!-- 标签页头部 -->
        <div class="tab-header">
            <div class="tab-item active">
                <i class="bi bi-person"></i>
                <span>系统用户</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-tools">
                <i class="bi bi-three-dots-vertical"></i>
            </div>
        </div>

        <!-- 系统用户内容 -->
        <div class="user-container">
            <div class="user-title">系统用户</div>
            
            <div class="user-filter">
                <div class="filter-item">
                    <span class="filter-label">选择部门:</span>
                    <select class="filter-select">
                        <option value="">请选择所属部门</option>
                        <option value="1">崇实科技</option>
                    </select>
                </div>
                
                <button class="btn btn-primary">
                    <i class="bi bi-search"></i>
                    查询
                </button>
                
                <button class="btn btn-add">
                    <i class="bi bi-plus"></i>
                    添加用户
                </button>
            </div>
            
            <div style="overflow-x: auto;">
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>用户名</th>
                            <th>账号</th>
                            <th>手机号</th>
                            <th>部门</th>
                            <th>角色</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>崇实科技</td>
                            <td>admin</td>
                            <td>17111111111</td>
                            <td>崇实科技</td>
                            <td>崇实科技管理员</td>
                            <td>
                                <i class="bi bi-pencil-square action-btn" title="编辑"></i>
                                <i class="bi bi-trash action-btn delete" title="删除"></i>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="pagination">
                <i class="bi bi-chevron-left page-btn"></i>
                <div class="page-btn active">1</div>
                <i class="bi bi-chevron-right page-btn"></i>
            </div>
        </div>
    </div>

    <!-- 导入组件脚本 -->
    <script src="components.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 阻止components.js中的自动初始化
            window.componentsInitialized = true;
            
            // 使用components.js中的函数渲染头部和侧边栏
            addGlobalStyles();
            renderHeader();
            renderSidebar('systemuser');
            
            // 确保系统管理菜单展开
            setTimeout(() => {
                const systemMenu = document.querySelector('.menu-item.with-submenu[data-title="系统管理"]');
                const systemSubmenu = systemMenu ? systemMenu.nextElementSibling : null;
                
                if (systemMenu) {
                    systemMenu.classList.add('active');
                }
                
                if (systemSubmenu) {
                    systemSubmenu.style.display = 'block';
                }
            }, 100);
            
            // 确保main-content在侧边栏之后
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                // 将main-content移到DOM树的最后，确保它在侧边栏之后
                document.body.appendChild(mainContent);
                
                // 设置main-content的样式
                mainContent.style.marginLeft = '210px';
                mainContent.style.transition = 'margin-left 0.3s';
                mainContent.style.width = 'calc(100% - 210px)';
                mainContent.style.float = 'right';
            }
            
            // 设置事件监听器
            setupEventListeners();
        });
    </script>
</body>
</html>
