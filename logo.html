<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实科技 Logo</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .logo-container {
            text-align: center;
        }
        .logo {
            width: 200px;
            height: 80px;
            margin-bottom: 20px;
        }
        .instructions {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .button {
            display: inline-block;
            background-color: #4ecdc4;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="logo-container">
        <svg class="logo" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
            <!-- 简单的 CS 标志 (崇实的拼音首字母) -->
            <rect width="200" height="80" fill="#ffffff" rx="10" ry="10" stroke="#4ecdc4" stroke-width="2"/>
            <text x="50" y="55" font-family="Arial, sans-serif" font-size="40" font-weight="bold" fill="#4ecdc4">CS</text>
            <text x="100" y="55" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#333333">科技</text>
        </svg>
        
        <div class="instructions">
            <h1>崇实科技 Logo</h1>
            <p>这是一个简单的 SVG 格式的崇实科技 Logo。您可以右键点击上面的 Logo，选择"图片另存为..."，将其保存为 PNG 文件。</p>
            <p>保存后，请确保将文件命名为 <strong>logo.png</strong> 并放置在项目的根目录中，以便系统正确显示。</p>
            <p>或者，您也可以使用自己的公司 Logo 替换此文件，只需确保文件名为 <strong>logo.png</strong> 即可。</p>
            
            <a href="#" class="button" onclick="downloadLogo()">下载 Logo</a>
        </div>
    </div>

    <script>
        function downloadLogo() {
            // 创建一个 Canvas 元素
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 80;
            const ctx = canvas.getContext('2d');
            
            // 绘制背景
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.roundRect(0, 0, 200, 80, 10);
            ctx.fill();
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制文字
            ctx.fillStyle = '#4ecdc4';
            ctx.font = 'bold 40px Arial';
            ctx.fillText('CS', 50, 55);
            
            ctx.fillStyle = '#333333';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('科技', 100, 55);
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = 'logo.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>
