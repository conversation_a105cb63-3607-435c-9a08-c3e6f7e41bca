<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>崇实科技AI加药系统 - 加药参数</title>
    <script src="js/chart.js"></script>
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
        }
        /* 主内容区域样式 */
        .main-content {
            position: relative;
            z-index: 1;
            padding: 20px;
            margin-left: 210px; /* 添加左边距，与侧边栏宽度相同 */
            transition: margin-left 0.3s, width 0.3s;
            width: calc(100% - 210px); /* 设置宽度，避免与侧边栏重叠 */
            float: right; /* 确保在侧边栏右侧 */
            box-sizing: border-box;
        }

        /* 当侧边栏折叠时，调整main-content的宽度 */
        .main-content.expanded {
            margin-left: 60px;
            width: calc(100% - 60px);
        }

        /* 侧边栏样式优化 */
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 210px;
            height: calc(100vh - 60px);
            background-color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 10;
            transition: width 0.3s;
        }

        .sidebar .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: #333;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: background-color 0.2s, border-left-color 0.2s;
        }

        .sidebar .menu-item:hover {
            background-color: #f5f5f5;
        }

        .sidebar .menu-item.active {
            border-left-color: #4ecdc4;
            background-color: #f9f9f9;
            color: #4ecdc4;
        }

        .sidebar .menu-item i {
            margin-right: 10px;
            font-size: 16px;
        }

        .sidebar .submenu {
            background-color: #f9f9f9;
            padding-left: 15px;
        }

        .sidebar .submenu .menu-item {
            padding: 10px 15px;
        }
        /* 导航栏样式由components.js提供 */
        .tab-header {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 1px;
        }
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
        }
        .tab-item.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .tab-item i {
            margin-right: 5px;
            font-size: 16px;
        }
        .tab-item .close-btn {
            margin-left: 8px;
            font-size: 14px;
            opacity: 0.6;
        }
        .tab-item:hover .close-btn {
            opacity: 1;
        }
        .tab-tools {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
        }
        .tab-tools i {
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
        }
        .status-panel {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: white;
            padding: 10px 20px;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .status-item {
            display: flex;
            align-items: center;
        }
        .status-label {
            margin-right: 10px;
            font-size: 14px;
        }
        .status-value {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        .status-normal {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        .status-warning {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        .status-danger {
            background-color: #fff1f0;
            color: #f5222d;
        }
        .signal-strength {
            display: flex;
            align-items: center;
        }
        .signal-bar {
            width: 3px;
            margin-right: 2px;
            background-color: #52c41a;
            border-radius: 1px;
        }
        .content-panel {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            display: block !important; /* 确保所有面板都显示 */
            visibility: visible !important; /* 确保所有面板都可见 */
            opacity: 1 !important; /* 确保所有面板都不透明 */
            width: 100%;
            clear: both;
            position: relative;
            z-index: 5; /* 提高z-index，确保在侧边栏之上 */
        }
        .panel-header {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 16px;
            font-weight: bold;
            background-color: #f5f5f5;
            color: #333;
        }
        .panel-content {
            padding: 15px;
            background-color: #fff;
        }
        .control-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 8px;
        }
        @media (min-width: 1200px) {
            .control-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }
        .control-block {
            background-color: white;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 8px;
        }
        .block-title {
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 8px;
            margin-bottom: 8px;
            font-weight: bold;
            font-size: 13px;
        }
        .control-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        .control-label {
            width: 60px;
            color: #666;
            font-size: 13px;
        }
        .control-value {
            flex: 1;
            display: flex;
            align-items: center;
        }
        .control-input {
            width: 50px;
            padding: 4px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            text-align: center;
            font-size: 13px;
        }
        .btn-modify {
            padding: 3px 6px;
            background-color: #4ecdc4;
            color: white;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            margin-left: 8px;
            font-size: 12px;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-on {
            background-color: #52c41a;
        }
        .status-off {
            background-color: #d9d9d9;
        }
        .btn-control {
            padding: 4px 10px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn-start {
            background-color: #52c41a;
            color: white;
        }
        .btn-stop {
            background-color: #f5222d;
            color: white;
        }
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
        }
        .action-primary {
            background-color: #f56262;
            color: white;
        }
        .btn-auto {
            background-color: #52c41a;
            color: white;
            padding: 3px 6px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
        }
        /* 侧边栏切换按钮样式优化 */
        .toggle-sidebar {
            position: fixed;
            left: 210px;
            top: 70px;
            width: 24px;
            height: 24px;
            background-color: #4ecdc4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            z-index: 100;
            transition: left 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .toggle-sidebar:hover {
            background-color: #3dbdb5;
            transform: scale(1.05);
        }

        .toggle-sidebar.collapsed {
            left: 60px;
        }

        /* 侧边栏折叠状态样式 */
        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar.collapsed .menu-item span,
        .sidebar.collapsed .menu-item.with-submenu::after,
        .sidebar.collapsed .submenu {
            display: none !important;
        }

        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 15px 0;
        }

        .sidebar.collapsed .menu-item i {
            margin-right: 0;
            font-size: 18px;
        }

        /* 侧边栏折叠时的悬停提示 */
        .sidebar.collapsed .menu-item {
            position: relative;
        }

        .sidebar.collapsed .menu-item:hover::after {
            content: attr(data-title);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            white-space: nowrap;
            z-index: 1000;
            font-size: 12px;
        }
        .main-content.expanded {
            margin-left: 60px;
        }
        .unit-text {
            margin-left: 5px;
            color: #999;
        }

        /* 药剂液位样式 */
        .drug-level {
            width: 100%;
            height: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin-top: 5px;
            overflow: hidden;
        }

        .drug-level-fill {
            height: 100%;
            background-color: #4ecdc4;
            border-radius: 5px;
        }

        .drug-level-text {
            text-align: right;
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        /* 按钮样式优化 */
        .btn-auto {
            background-color: #52c41a;
            color: white;
            padding: 3px 6px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-container {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            width: 800px;
            max-width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .modal-close {
            cursor: pointer;
            font-size: 20px;
            color: #999;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .modal-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .modal-btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .modal-btn-default {
            background-color: #f0f0f0;
            color: #333;
        }

        /* 预警配置表格样式 */
        .warning-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .warning-table th, .warning-table td {
            border: 1px solid #e8e8e8;
            padding: 12px 15px;
            text-align: center;
        }

        .warning-table th {
            background-color: #f5f5f5;
            font-weight: normal;
            color: #666;
        }

        .warning-table tr:nth-child(even) {
            background-color: #fafafa;
        }

        .warning-input {
            width: 80px;
            padding: 6px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            text-align: center;
        }

        .warning-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 2px;
            font-size: 12px;
        }

        .warning-status-on {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .warning-status-off {
            background-color: #f5f5f5;
            color: #999;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #52c41a;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }
    </style>
</head>
<body>
    <!-- 头部和侧栏将由组件动态插入 -->

    <div class="main-content">
        <!-- 标签页头部 -->
        <div class="tab-header">
            <div class="tab-item" onclick="window.location.href='index.html'">
                <i class="bi bi-gear-fill"></i>
                <span>运行情况</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-item" onclick="window.location.href='analyze.html'">
                <i class="bi bi-graph-up"></i>
                <span>效益分析</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-item" onclick="window.location.href='waterquality.html'">
                <i class="bi bi-droplet-fill"></i>
                <span>水质情况</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-item active">
                <i class="bi bi-sliders"></i>
                <span>加药参数</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-tools">
                <i class="bi bi-three-dots-vertical"></i>
            </div>
        </div>

        <!-- 运行状态面板 -->
        <div class="status-panel">
            <button class="action-btn action-primary">一键绑定</button>

            <div class="status-item">
                <div class="status-label">算法已停止</div>
                <i class="bi bi-info-circle" style="color: #faad14; margin-left: 5px;"></i>
            </div>

            <div class="status-item">
                <div class="status-label">现场设备状态:</div>
                <div class="status-value status-danger">本地控制</div>
            </div>

            <div class="status-item">
                <div class="status-label">排放口通讯状态:</div>
                <div class="status-value status-normal">通信正常</div>
            </div>

            <div class="status-item">
                <div class="status-label">PLC通讯状态:</div>
                <div class="status-value status-normal">通信正常</div>
            </div>

            <div class="status-item">
                <div class="status-label">信号强度:</div>
                <div class="signal-strength">
                    <div class="signal-bar" style="height: 6px;"></div>
                    <div class="signal-bar" style="height: 9px;"></div>
                    <div class="signal-bar" style="height: 12px;"></div>
                    <div class="signal-bar" style="height: 15px;"></div>
                    <span style="margin-left: 5px;">18</span>
                </div>
            </div>

            <button class="action-btn" style="background-color: #1890ff; color: white;" id="warningConfigBtn">预警配置</button>
        </div>

        <!-- 预警配置弹窗 -->
        <div class="modal-overlay" id="warningConfigModal">
            <div class="modal-container">
                <div class="modal-header">
                    <div class="modal-title">预警配置</div>
                    <div class="modal-close" id="closeWarningModal">&times;</div>
                </div>
                <div class="modal-body">
                    <table class="warning-table">
                        <thead>
                            <tr>
                                <th></th>
                                <th>下限</th>
                                <th>上限</th>
                                <th>偏移量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>液碱氧化池出pH:</td>
                                <td>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="naohLowerSwitch" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                    <input type="text" class="warning-input" value="3">
                                </td>
                                <td>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="naohUpperSwitch" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                    <input type="text" class="warning-input" value="7">
                                </td>
                                <td>
                                    <span>偏移量</span>
                                    <input type="text" class="warning-input" value="0.5">
                                </td>
                            </tr>
                            <tr>
                                <td>废酸氧化池出pH:</td>
                                <td>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="acidLowerSwitch" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                    <input type="text" class="warning-input" value="3">
                                </td>
                                <td>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="acidUpperSwitch" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                    <input type="text" class="warning-input" value="7">
                                </td>
                                <td>
                                    <span>偏移量</span>
                                    <input type="text" class="warning-input" value="0.5">
                                </td>
                            </tr>
                            <tr>
                                <td>终沉池出水pH:</td>
                                <td>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="finalLowerSwitch" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                    <input type="text" class="warning-input" value="3">
                                </td>
                                <td>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="finalUpperSwitch" checked>
                                        <span class="toggle-slider"></span>
                                    </div>
                                    <input type="text" class="warning-input" value="7">
                                </td>
                                <td>
                                    <span>偏移量</span>
                                    <input type="text" class="warning-input" value="0.5">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button class="modal-btn modal-btn-default" id="cancelWarningConfig">取消</button>
                    <button class="modal-btn modal-btn-primary" id="confirmWarningConfig">确定</button>
                </div>
            </div>
        </div>

        <!-- 盐酸控制面板 -->
        <div class="content-panel">
            <div class="panel-header">乙酸钠</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定A</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="1">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定B</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="1">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵A运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵B运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵C运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵D运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 固体硫酸亚铁控制面板 -->
        <div class="content-panel">
            <div class="panel-header">聚合氯化铝（PAC）</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定A</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="1">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定B</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="3">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵A运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-on"></div>
                                <span>启动</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵B运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵C运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵D运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 液碱控制面板 -->
        <div class="content-panel">
            <div class="panel-header">硫酸铝（AI2SO4）</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定A</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="2">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定B</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="3">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定C</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="2.3">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵A运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵B运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵C运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵D运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">流量</div>
                            <div class="control-value">
                                <span>0 L/h</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 聚丙烯酰胺控制面板 -->
        <div class="content-panel">
            <div class="panel-header">聚丙烯酰胺（PAM）</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定A</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="1">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定B</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="2">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵A运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵B运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵C运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵D运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-on"></div>
                                <span>启动</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<!-- 除氟剂投加智能控制 -->
        <div class="content-panel">
            <div class="panel-header">除氟剂</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定A</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="1">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定B</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="2">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵A运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵B运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵C运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵D运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-on"></div>
                                <span>启动</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 废酸-低聚铁面板 -->
        <div class="content-panel">
            <div class="panel-header">氢氧化钠</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定A</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="1">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定B</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="2">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵A运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵B运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵C运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵D运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-on"></div>
                                <span>启动</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 废酸-液体硫酸亚铁控制面板 -->
        <div class="content-panel">
            <div class="panel-header">次氯酸钠</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定A</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="3">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加量设定B</div>
                            <div class="control-value">
                                <input type="text" class="control-input" value="1">
                                <button class="btn-modify">修改</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵A运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵B运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵C运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-on"></div>
                                <span>启动</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">投加泵D运行状态</div>
                            <div class="control-value">
                                <div class="status-indicator status-off"></div>
                                <span>停止</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加药量计算面板 -->
        <div class="content-panel">
            <div class="panel-header">加药量计算</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">乙酸钠投加量</div>
                            <div class="control-value">
                                <span>2.5 kg/h</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">聚合氯化铝（PAC）投加量</div>
                            <div class="control-value">
                                <span>15.2 kg/h</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">硫酸铝（AI2SO4）投加量</div>
                            <div class="control-value">
                                <span>8.7 kg/h</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">聚丙烯酰胺（PAM）投加量</div>
                            <div class="control-value">
                                <span>1.2 kg/h</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">除氟剂投加量</div>
                            <div class="control-value">
                                <span>3.8 kg/h</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">氢氧化钠投加量</div>
                            <div class="control-value">
                                <span>5.6 kg/h</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">次氯酸钠投加量</div>
                            <div class="control-value">
                                <span>4.3 L/h</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 排放口面板 -->
        <div class="content-panel">
            <div class="panel-header">排放口</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">氨氮</div>
                            <div class="control-value">
                                <span>0.06 mg/L</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">COD</div>
                            <div class="control-value">
                                <span>53.74 mg/L</span>
                            </div>
                        </div>
                    </div>

                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">流量值</div>
                            <div class="control-value">
                                <span>4064.4 m<sup>3</sup>/h</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 累计流量 -->
        <div class="content-panel">
            <div class="panel-header">累计</div>
            <div class="panel-content">
                <div class="control-grid">
                    <div class="control-block">
                        <div class="control-row">
                            <div class="control-label">流量值</div>
                            <div class="control-value">
                                <span>93619696 m<sup>3</sup></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入组件脚本 -->
    <script src="components.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 阻止components.js中的自动初始化
            window.componentsInitialized = true;

            // 使用components.js中的函数渲染头部和侧边栏
            addGlobalStyles();
            renderHeader();
            renderSidebar('drugparam');

            // 确保main-content在侧边栏之后
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                // 将main-content移到DOM树的最后，确保它在侧边栏之后
                document.body.appendChild(mainContent);

                // 设置main-content的样式
                mainContent.style.marginLeft = '210px';
                mainContent.style.transition = 'margin-left 0.3s';
                mainContent.style.width = 'calc(100% - 210px)';
                mainContent.style.float = 'right';
            }

            // 确保所有内容面板都显示
            const contentPanels = document.querySelectorAll('.content-panel');
            contentPanels.forEach(panel => {
                panel.style.display = 'block';
                panel.style.visibility = 'visible';
                panel.style.opacity = '1';
            });

            // 标签页切换功能
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // 如果点击的是关闭按钮，不执行切换操作
                    if (e.target.classList.contains('close-btn') || e.target.parentElement.classList.contains('close-btn')) {
                        return;
                    }

                    // 如果有onclick属性，执行默认行为
                    if (tab.hasAttribute('onclick')) {
                        return;
                    }

                    // 否则切换活动标签
                    tabItems.forEach(item => item.classList.remove('active'));
                    tab.classList.add('active');
                });
            });

            // 设置事件监听器
            setupEventListeners();

            // 预警配置弹窗功能
            const warningConfigBtn = document.getElementById('warningConfigBtn');
            const warningConfigModal = document.getElementById('warningConfigModal');
            const closeWarningModal = document.getElementById('closeWarningModal');
            const cancelWarningConfig = document.getElementById('cancelWarningConfig');
            const confirmWarningConfig = document.getElementById('confirmWarningConfig');

            if (warningConfigBtn && warningConfigModal) {
                // 打开弹窗
                warningConfigBtn.addEventListener('click', function() {
                    warningConfigModal.style.display = 'flex';
                });

                // 关闭弹窗 - 点击关闭按钮
                if (closeWarningModal) {
                    closeWarningModal.addEventListener('click', function() {
                        warningConfigModal.style.display = 'none';
                    });
                }

                // 关闭弹窗 - 点击取消按钮
                if (cancelWarningConfig) {
                    cancelWarningConfig.addEventListener('click', function() {
                        warningConfigModal.style.display = 'none';
                    });
                }

                // 确认配置 - 点击确定按钮
                if (confirmWarningConfig) {
                    confirmWarningConfig.addEventListener('click', function() {
                        // 这里可以添加保存配置的逻辑
                        warningConfigModal.style.display = 'none';

                        // 显示保存成功提示
                        alert('预警配置已保存');
                    });
                }

                // 点击弹窗外部关闭弹窗
                warningConfigModal.addEventListener('click', function(e) {
                    if (e.target === warningConfigModal) {
                        warningConfigModal.style.display = 'none';
                    }
                });
            }

            // 侧边栏切换按钮功能
            const toggleBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn && sidebar && mainContent) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                    toggleBtn.classList.toggle('collapsed');

                    // 更新main-content的样式
                    if (sidebar.classList.contains('collapsed')) {
                        mainContent.style.marginLeft = '60px';
                        mainContent.style.width = 'calc(100% - 60px)';
                    } else {
                        mainContent.style.marginLeft = '210px';
                        mainContent.style.width = 'calc(100% - 210px)';
                    }

                    // 切换图标
                    const icon = toggleBtn.querySelector('i');
                    if (sidebar.classList.contains('collapsed')) {
                        icon.classList.remove('bi-chevron-left');
                        icon.classList.add('bi-chevron-right');
                    } else {
                        icon.classList.remove('bi-chevron-right');
                        icon.classList.add('bi-chevron-left');
                    }
                });
            }

            // 确保项目管理子菜单展开，并且加药参数菜单项高亮
            const projectMenu = document.querySelector('.menu-item.with-submenu');
            const projectSubmenu = projectMenu ? projectMenu.nextElementSibling : null;
            const drugParamMenuItem = document.querySelector('a[href="drugparam.html"]');

            if (projectMenu) {
                projectMenu.classList.add('active');
            }

            if (projectSubmenu) {
                projectSubmenu.style.display = 'block';
            }

            if (drugParamMenuItem) {
                drugParamMenuItem.classList.add('active');
            }
        });
    </script>
</body>
</html>