<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常情况记录 - 崇实科技AI加药系统</title>
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        body {
            background-color: #f5f7fa;
        }
        /* 主内容区域样式 */
        .main-content {
            position: relative;
            z-index: 1;
            padding: 20px;
            margin-left: 210px; /* 添加左边距，与侧边栏宽度相同 */
            transition: margin-left 0.3s, width 0.3s;
            width: calc(100% - 210px); /* 设置宽度，避免与侧边栏重叠 */
            float: right; /* 确保在侧边栏右侧 */
            box-sizing: border-box;
        }

        /* 当侧边栏折叠时，调整main-content的宽度 */
        .main-content.expanded {
            margin-left: 60px;
            width: calc(100% - 60px);
        }

        /* 侧边栏样式优化 */
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 210px;
            height: calc(100vh - 60px);
            background-color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 10;
            transition: width 0.3s;
        }

        .sidebar .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: #333;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: background-color 0.2s, border-left-color 0.2s;
        }

        .sidebar .menu-item:hover {
            background-color: #f5f5f5;
        }

        .sidebar .menu-item.active {
            border-left-color: #4ecdc4;
            background-color: #f9f9f9;
            color: #4ecdc4;
        }

        .sidebar .menu-item i {
            margin-right: 10px;
            font-size: 16px;
        }

        .sidebar .submenu {
            background-color: #f9f9f9;
            padding-left: 15px;
        }

        .sidebar .submenu .menu-item {
            padding: 10px 15px;
        }

        /* 侧边栏切换按钮样式优化 */
        .toggle-sidebar {
            position: fixed;
            left: 210px;
            top: 70px;
            width: 24px;
            height: 24px;
            background-color: #4ecdc4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            z-index: 100;
            transition: left 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .toggle-sidebar:hover {
            background-color: #3dbdb5;
            transform: scale(1.05);
        }

        .toggle-sidebar.collapsed {
            left: 60px;
        }

        /* 侧边栏折叠状态样式 */
        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar.collapsed .menu-item span,
        .sidebar.collapsed .menu-item.with-submenu::after,
        .sidebar.collapsed .submenu {
            display: none !important;
        }

        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 15px 0;
        }

        .sidebar.collapsed .menu-item i {
            margin-right: 0;
            font-size: 18px;
        }

        /* 侧边栏折叠时的悬停提示 */
        .sidebar.collapsed .menu-item {
            position: relative;
        }

        .sidebar.collapsed .menu-item:hover::after {
            content: attr(data-title);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            white-space: nowrap;
            z-index: 1000;
            font-size: 12px;
        }

        /* 标签页样式 */
        .tab-header {
            display: flex;
            background-color: #fff;
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            margin-bottom: 1px;
        }
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            position: relative;
            display: flex;
            align-items: center;
        }
        .tab-item.active {
            border-bottom-color: #4ecdc4;
            color: #4ecdc4;
        }
        .tab-item i {
            margin-right: 5px;
            font-size: 16px;
        }
        .tab-item .close-btn {
            margin-left: 8px;
            font-size: 14px;
            opacity: 0.6;
        }
        .tab-item:hover .close-btn {
            opacity: 1;
        }
        .tab-tools {
            display: flex;
            align-items: center;
            margin-left: auto;
            margin-right: 10px;
        }
        .tab-tools i {
            margin-left: 15px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
        }

        /* 报表内容样式 */
        .report-container {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 20px;
        }
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .report-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .date-picker {
            display: flex;
            align-items: center;
        }
        .date-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 10px;
            margin-right: 10px;
        }
        .report-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #4ecdc4;
            color: white;
        }
        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
        }

        /* 表格样式 */
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 14px;
        }
        .report-table th, .report-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #eee;
        }
        .report-table th {
            background-color: #f5f5f5;
            font-weight: normal;
            color: #666;
        }
        .report-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .report-table tr:hover {
            background-color: #f0f0f0;
        }

        /* 无数据提示 */
        .no-data {
            text-align: center;
            padding: 50px 0;
            color: #999;
        }
        .no-data-icon {
            font-size: 60px;
            margin-bottom: 20px;
            color: #ddd;
        }

        /* 下拉选择框样式 */
        .select-container {
            display: flex;
            align-items: center;
        }
        .select-box {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            margin-left: 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- 头部和侧栏将由组件动态插入 -->

    <div class="main-content">
        <!-- 标签页头部 -->
        <div class="tab-header">
            <div class="tab-item" onclick="window.location.href='index.html'">
                <i class="bi bi-gear-fill"></i>
                <span>运行情况</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-item" onclick="window.location.href='analyze.html'">
                <i class="bi bi-graph-up"></i>
                <span>效益分析</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-item" onclick="window.location.href='waterquality.html'">
                <i class="bi bi-droplet-fill"></i>
                <span>水质情况</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-item active">
                <i class="bi bi-exclamation-triangle"></i>
                <span>异常情况记录</span>
                <i class="close-btn bi bi-x"></i>
            </div>
            <div class="tab-tools">
                <i class="bi bi-three-dots-vertical"></i>
            </div>
        </div>

        <!-- 报表内容 -->
        <div class="report-container">
            <div class="report-header">
                <div class="report-title">异常情况记录</div>
                <div class="date-picker">
                    <span>选择时间:</span>
                    <input type="date" class="date-input" value="2023-05-08">
                    <span>—</span>
                    <input type="date" class="date-input" value="2023-05-08">
                </div>
                <div class="select-container">
                    <span>数据类型:</span>
                    <select class="select-box">
                        <option value="all">请选择</option>
                        <option value="hardware">硬件故障</option>
                        <option value="software">软件故障</option>
                        <option value="network">网络故障</option>
                        <option value="other">其他故障</option>
                    </select>
                </div>
                <div class="report-actions">
                    <button class="btn btn-primary">下载数据</button>
                </div>
            </div>

            <div style="overflow-x: auto;">
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>故障字段</th>
                            <th>故障类型</th>
                            <th>故障详情</th>
                            <th>故障时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 无数据提示 -->
                        <tr>
                            <td colspan="5">
                                <div class="no-data">
                                    <div class="no-data-icon">
                                        <i class="bi bi-inbox"></i>
                                    </div>
                                    <div>暂无数据</div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 导入组件脚本 -->
    <script src="components.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 阻止components.js中的自动初始化
            window.componentsInitialized = true;

            // 使用components.js中的函数渲染头部和侧边栏
            addGlobalStyles();
            renderHeader();
            renderSidebar('abnormalrecord');

            // 确保报表管理菜单展开
            setTimeout(() => {
                const reportMenu = document.querySelector('.menu-item.with-submenu[data-title="报表管理"]');
                const reportSubmenu = reportMenu ? reportMenu.nextElementSibling : null;

                if (reportMenu) {
                    reportMenu.classList.add('active');
                }

                if (reportSubmenu) {
                    reportSubmenu.style.display = 'block';
                }
            }, 100);

            // 确保main-content在侧边栏之后
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                // 将main-content移到DOM树的最后，确保它在侧边栏之后
                document.body.appendChild(mainContent);

                // 设置main-content的样式
                mainContent.style.marginLeft = '210px';
                mainContent.style.transition = 'margin-left 0.3s';
                mainContent.style.width = 'calc(100% - 210px)';
                mainContent.style.float = 'right';
            }

            // 设置事件监听器
            setupEventListeners();

            // 侧边栏切换按钮功能
            const toggleBtn = document.querySelector('.toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');

            if (toggleBtn && sidebar && mainContent) {
                toggleBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                    toggleBtn.classList.toggle('collapsed');

                    // 更新main-content的样式
                    if (sidebar.classList.contains('collapsed')) {
                        mainContent.style.marginLeft = '60px';
                        mainContent.style.width = 'calc(100% - 60px)';
                    } else {
                        mainContent.style.marginLeft = '210px';
                        mainContent.style.width = 'calc(100% - 210px)';
                    }

                    // 切换图标
                    const icon = toggleBtn.querySelector('i');
                    if (sidebar.classList.contains('collapsed')) {
                        icon.classList.remove('bi-chevron-left');
                        icon.classList.add('bi-chevron-right');
                    } else {
                        icon.classList.remove('bi-chevron-right');
                        icon.classList.add('bi-chevron-left');
                    }
                });
            }
        });
    </script>
</body>
</html>
