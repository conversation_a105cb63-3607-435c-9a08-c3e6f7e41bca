# 崇实科技AI加药系统界面

这是一个基于HTML/CSS/JavaScript实现的崇实科技AI加药系统界面，模仿了截图中的系统设计。

## 特点

- 响应式布局设计
- 使用Chart.js实现数据可视化
- 级联菜单系统和模拟数据展示
- 进度条和状态指示器

## 使用方法

1. 直接打开`index.html`文件即可查看界面
2. 界面展示了药剂流量、药剂状态和药剂液位等信息
3. 点击左侧菜单可以导航到不同功能区域，包含子菜单展开功能

## 文件结构

- `index.html` - 主界面文件，包含内联SVG作为logo

## 菜单结构

- 概览大屏
- 项目管理
  - 运行情况
  - 效益分析
  - 水质情况
  - 加药参数
  - 药剂信息
- 报表管理
  - 系统药剂报表
  - 水质情况报表
  - 吨水药耗报表
  - 吨水费用报表
  - 月度节约报表
  - 异常情况报表
- 系统管理
  - 部门管理
  - 系统角色
  - 系统用户
- 日志管理
  - 日志列表

## 技术栈

- HTML5
- CSS3
- JavaScript
- Chart.js - 用于图表显示

## 数据说明

目前系统使用的是模拟数据，在实际应用中可以替换为实时数据API。 